description = 'Maling Graph Documentation - 说明书生成器'

dependencies {
    // 依赖核心模块
    api project(':maling-graph-base')
    api project(':maling-graph-repository')
    api project(':maling-graph-ai')

    // Spring Framework
    implementation "org.springframework:spring-context:${springVersion}"
    implementation "org.springframework:spring-web:${springVersion}"
    implementation "org.springframework:spring-tx:${springVersion}"

    // Spring AI (如果项目中已有版本变量)
    implementation "org.springframework.ai:spring-ai-openai-spring-boot-starter:1.0.1"

    // MyBatis
    implementation "org.mybatis:mybatis:3.5.13"
    implementation "org.mybatis:mybatis-spring:3.0.3"

    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"

    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}
