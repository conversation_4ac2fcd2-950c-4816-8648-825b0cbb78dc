package com.webank.maling.documentation.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 流程说明书实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlowDocumentationEntity {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 流程名称
     */
    private String flowName;
    
    /**
     * 入口点方法全名
     */
    private String entryPoint;
    
    /**
     * 入口点节点ID（在图数据库中的ID）
     */
    private String entryPointNodeId;
    
    /**
     * 说明书内容（AI生成的完整说明书）
     */
    private String documentation;
    
    /**
     * 生成状态：PENDING, IN_PROGRESS, COMPLETED, FAILED
     */
    private String status;
    
    /**
     * 生成进度（0-100）
     */
    private Integer progress;
    
    /**
     * 当前处理的层级
     */
    private Integer currentLayer;
    
    /**
     * 总层级数
     */
    private Integer totalLayers;
    
    /**
     * 涉及的节点总数
     */
    private Integer totalNodes;
    
    /**
     * 已处理的节点数
     */
    private Integer processedNodes;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 项目ID
     */
    private String projectId;
    
    /**
     * 分支名称
     */
    private String branchName;
    
    /**
     * 错误信息（如果生成失败）
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 完成时间
     */
    private LocalDateTime completedAt;
}
