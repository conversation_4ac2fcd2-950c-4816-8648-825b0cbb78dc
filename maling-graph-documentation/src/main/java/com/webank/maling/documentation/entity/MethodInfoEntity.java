package com.webank.maling.documentation.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 方法信息实体类
 * 记录说明书涉及的所有方法信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MethodInfoEntity {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 说明书ID（外键）
     */
    private Long documentationId;
    
    /**
     * 方法节点ID（在图数据库中的ID）
     */
    private String nodeId;
    
    /**
     * 方法全名
     */
    private String methodFullName;
    
    /**
     * 方法名
     */
    private String methodName;
    
    /**
     * 类名
     */
    private String className;
    
    /**
     * 包名
     */
    private String packageName;
    
    /**
     * 方法签名
     */
    private String methodSignature;
    
    /**
     * 返回类型
     */
    private String returnType;
    
    /**
     * 参数列表（JSON格式）
     */
    private String parameters;
    
    /**
     * 可见性：public, private, protected, package
     */
    private String visibility;
    
    /**
     * 是否静态方法
     */
    private Boolean isStatic;
    
    /**
     * 是否抽象方法
     */
    private Boolean isAbstract;
    
    /**
     * 调用层级（从入口点开始的深度）
     */
    private Integer callLevel;
    
    /**
     * 方法类型：ENTRY_POINT, CONTROLLER, SERVICE, REPOSITORY, UTILITY, OTHER
     */
    private String methodType;
    
    /**
     * 方法描述（从注释中提取）
     */
    private String description;
    
    /**
     * 方法代码片段
     */
    private String codeSnippet;
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 行号
     */
    private Integer lineNumber;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
