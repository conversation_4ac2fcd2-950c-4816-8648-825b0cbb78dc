package com.webank.maling.documentation.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 数据库操作模型
 */
@Data
@Builder
public class DatabaseOperation {
    
    /**
     * 方法名（包含类名）
     */
    private String methodName;
    
    /**
     * 操作类型
     */
    private SqlOperation.OperationType operationType;
    
    /**
     * 涉及的表名列表
     */
    private List<String> tables;
    
    /**
     * 涉及的字段列表
     */
    private List<String> fields;
    
    /**
     * 参数列表
     */
    private List<String> parameters;
    
    /**
     * SQL模板
     */
    private String sqlTemplate;
    
    /**
     * 返回类型
     */
    private String returnType;
    
    /**
     * 描述
     */
    private String description;
}
