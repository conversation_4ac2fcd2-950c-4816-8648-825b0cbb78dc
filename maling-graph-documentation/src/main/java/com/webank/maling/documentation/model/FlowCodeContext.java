package com.webank.maling.documentation.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 流程代码上下文
 * 包含生成说明书所需的所有代码信息
 */
@Data
@Builder
public class FlowCodeContext {
    
    /**
     * 入口点代码
     */
    private String entryPointCode;
    
    /**
     * 调用的方法列表
     */
    private List<String> calledMethods;
    
    /**
     * 相关数据模型代码
     */
    private List<String> dataModels;
    
    /**
     * 相关配置信息
     */
    private List<String> configurations;
    
    /**
     * MyBatis XML文件路径
     */
    private List<String> mapperXmlFiles;
    
    /**
     * 注解形式的Mapper接口
     */
    private List<String> mapperInterfaces;
    
    /**
     * JPA Repository接口
     */
    private List<String> repositoryInterfaces;
    
    /**
     * 相关的Service类
     */
    private List<String> serviceClasses;
    
    /**
     * 相关的Controller类
     */
    private List<String> controllerClasses;
    
    /**
     * 异常处理类
     */
    private List<String> exceptionClasses;
    
    /**
     * 配置类
     */
    private List<String> configurationClasses;
    
    /**
     * 工具类
     */
    private List<String> utilityClasses;
}
