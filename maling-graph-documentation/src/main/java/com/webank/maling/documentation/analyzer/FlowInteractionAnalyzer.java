package com.webank.maling.documentation.analyzer;

import com.webank.maling.documentation.model.DatabaseOperation;
import com.webank.maling.documentation.model.FlowInteraction;
import com.webank.maling.documentation.model.SqlOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程交互分析器
 * 分析不同流程之间通过数据库的交互关系
 */
@Slf4j
public class FlowInteractionAnalyzer {
    
    /**
     * 分析流程间的数据交互
     * 
     * @param flowOperations 各个流程的数据库操作映射
     * @return 流程交互关系列表
     */
    public List<FlowInteraction> analyzeFlowInteractions(Map<String, List<DatabaseOperation>> flowOperations) {
        List<FlowInteraction> interactions = new ArrayList<>();
        
        // 构建表字段的读写映射
        Map<String, Set<String>> tableWriters = new HashMap<>();  // 表 -> 写入该表的流程集合
        Map<String, Set<String>> tableReaders = new HashMap<>();  // 表 -> 读取该表的流程集合
        Map<String, Map<String, Set<String>>> fieldWriters = new HashMap<>(); // 表 -> 字段 -> 写入该字段的流程集合
        Map<String, Map<String, Set<String>>> fieldReaders = new HashMap<>(); // 表 -> 字段 -> 读取该字段的流程集合
        
        // 收集读写信息
        for (Map.Entry<String, List<DatabaseOperation>> entry : flowOperations.entrySet()) {
            String flowName = entry.getKey();
            List<DatabaseOperation> operations = entry.getValue();
            
            for (DatabaseOperation operation : operations) {
                for (String table : operation.getTables()) {
                    if (isWriteOperation(operation.getOperationType())) {
                        tableWriters.computeIfAbsent(table, k -> new HashSet<>()).add(flowName);
                        
                        // 记录字段级别的写入
                        Map<String, Set<String>> tableFieldWriters = fieldWriters.computeIfAbsent(table, k -> new HashMap<>());
                        for (String field : operation.getFields()) {
                            tableFieldWriters.computeIfAbsent(field, k -> new HashSet<>()).add(flowName);
                        }
                    } else if (isReadOperation(operation.getOperationType())) {
                        tableReaders.computeIfAbsent(table, k -> new HashSet<>()).add(flowName);
                        
                        // 记录字段级别的读取
                        Map<String, Set<String>> tableFieldReaders = fieldReaders.computeIfAbsent(table, k -> new HashMap<>());
                        for (String field : operation.getFields()) {
                            tableFieldReaders.computeIfAbsent(field, k -> new HashSet<>()).add(flowName);
                        }
                    }
                }
            }
        }
        
        // 分析交互关系
        interactions.addAll(analyzeProducerConsumerInteractions(tableWriters, tableReaders, fieldWriters, fieldReaders));
        interactions.addAll(analyzeCompetingConsumerInteractions(tableWriters, tableReaders));
        interactions.addAll(analyzeSharedDataInteractions(tableWriters, tableReaders));
        
        return interactions;
    }
    
    /**
     * 分析生产者-消费者交互（A写入，B读取）
     */
    private List<FlowInteraction> analyzeProducerConsumerInteractions(
            Map<String, Set<String>> tableWriters,
            Map<String, Set<String>> tableReaders,
            Map<String, Map<String, Set<String>>> fieldWriters,
            Map<String, Map<String, Set<String>>> fieldReaders) {
        
        List<FlowInteraction> interactions = new ArrayList<>();
        
        for (String table : tableWriters.keySet()) {
            Set<String> writers = tableWriters.get(table);
            Set<String> readers = tableReaders.getOrDefault(table, new HashSet<>());
            
            for (String writer : writers) {
                for (String reader : readers) {
                    if (!writer.equals(reader)) {
                        // 分析具体的字段交互
                        Set<String> sharedFields = findSharedFields(table, writer, reader, fieldWriters, fieldReaders);
                        
                        FlowInteraction interaction = FlowInteraction.builder()
                                .producerFlow(writer)
                                .consumerFlow(reader)
                                .interactionType(FlowInteraction.InteractionType.PRODUCER_CONSUMER)
                                .dataTable(table)
                                .sharedFields(new ArrayList<>(sharedFields))
                                .description(String.format("流程 %s 写入表 %s，流程 %s 读取相同数据", writer, table, reader))
                                .build();
                        
                        interactions.add(interaction);
                    }
                }
            }
        }
        
        return interactions;
    }
    
    /**
     * 分析竞争消费者交互（多个流程读取同一数据）
     */
    private List<FlowInteraction> analyzeCompetingConsumerInteractions(
            Map<String, Set<String>> tableWriters,
            Map<String, Set<String>> tableReaders) {
        
        List<FlowInteraction> interactions = new ArrayList<>();
        
        for (String table : tableReaders.keySet()) {
            Set<String> readers = tableReaders.get(table);
            if (readers.size() > 1) {
                List<String> readerList = new ArrayList<>(readers);
                for (int i = 0; i < readerList.size(); i++) {
                    for (int j = i + 1; j < readerList.size(); j++) {
                        FlowInteraction interaction = FlowInteraction.builder()
                                .producerFlow(readerList.get(i))
                                .consumerFlow(readerList.get(j))
                                .interactionType(FlowInteraction.InteractionType.COMPETING_CONSUMER)
                                .dataTable(table)
                                .description(String.format("流程 %s 和 %s 都读取表 %s，可能存在竞争关系", 
                                           readerList.get(i), readerList.get(j), table))
                                .build();
                        
                        interactions.add(interaction);
                    }
                }
            }
        }
        
        return interactions;
    }
    
    /**
     * 分析共享数据交互（多个流程写入同一数据）
     */
    private List<FlowInteraction> analyzeSharedDataInteractions(
            Map<String, Set<String>> tableWriters,
            Map<String, Set<String>> tableReaders) {
        
        List<FlowInteraction> interactions = new ArrayList<>();
        
        for (String table : tableWriters.keySet()) {
            Set<String> writers = tableWriters.get(table);
            if (writers.size() > 1) {
                List<String> writerList = new ArrayList<>(writers);
                for (int i = 0; i < writerList.size(); i++) {
                    for (int j = i + 1; j < writerList.size(); j++) {
                        FlowInteraction interaction = FlowInteraction.builder()
                                .producerFlow(writerList.get(i))
                                .consumerFlow(writerList.get(j))
                                .interactionType(FlowInteraction.InteractionType.SHARED_DATA)
                                .dataTable(table)
                                .description(String.format("流程 %s 和 %s 都写入表 %s，共享数据操作", 
                                           writerList.get(i), writerList.get(j), table))
                                .build();
                        
                        interactions.add(interaction);
                    }
                }
            }
        }
        
        return interactions;
    }
    
    /**
     * 查找共享字段
     */
    private Set<String> findSharedFields(String table, String writer, String reader,
                                       Map<String, Map<String, Set<String>>> fieldWriters,
                                       Map<String, Map<String, Set<String>>> fieldReaders) {
        Set<String> sharedFields = new HashSet<>();
        
        Map<String, Set<String>> tableFieldWriters = fieldWriters.get(table);
        Map<String, Set<String>> tableFieldReaders = fieldReaders.get(table);
        
        if (tableFieldWriters != null && tableFieldReaders != null) {
            for (String field : tableFieldWriters.keySet()) {
                if (tableFieldWriters.get(field).contains(writer) && 
                    tableFieldReaders.getOrDefault(field, new HashSet<>()).contains(reader)) {
                    sharedFields.add(field);
                }
            }
        }
        
        return sharedFields;
    }
    
    /**
     * 判断是否为写操作
     */
    private boolean isWriteOperation(SqlOperation.OperationType operationType) {
        return operationType == SqlOperation.OperationType.INSERT ||
               operationType == SqlOperation.OperationType.UPDATE ||
               operationType == SqlOperation.OperationType.DELETE;
    }
    
    /**
     * 判断是否为读操作
     */
    private boolean isReadOperation(SqlOperation.OperationType operationType) {
        return operationType == SqlOperation.OperationType.SELECT;
    }
    
    /**
     * 生成交互关系图的DOT格式
     */
    public String generateInteractionDiagram(List<FlowInteraction> interactions) {
        StringBuilder dot = new StringBuilder();
        dot.append("digraph FlowInteractions {\n");
        dot.append("  rankdir=LR;\n");
        dot.append("  node [shape=box];\n\n");
        
        Set<String> flows = new HashSet<>();
        for (FlowInteraction interaction : interactions) {
            flows.add(interaction.getProducerFlow());
            flows.add(interaction.getConsumerFlow());
        }
        
        // 添加节点
        for (String flow : flows) {
            dot.append(String.format("  \"%s\" [label=\"%s\"];\n", flow, flow));
        }
        
        dot.append("\n");
        
        // 添加边
        for (FlowInteraction interaction : interactions) {
            String color = getInteractionColor(interaction.getInteractionType());
            String label = String.format("%s\\n(%s)", interaction.getDataTable(), 
                                       interaction.getInteractionType().getDescription());
            
            dot.append(String.format("  \"%s\" -> \"%s\" [label=\"%s\", color=\"%s\"];\n",
                                   interaction.getProducerFlow(),
                                   interaction.getConsumerFlow(),
                                   label,
                                   color));
        }
        
        dot.append("}\n");
        return dot.toString();
    }
    
    /**
     * 获取交互类型对应的颜色
     */
    private String getInteractionColor(FlowInteraction.InteractionType type) {
        switch (type) {
            case PRODUCER_CONSUMER:
                return "blue";
            case COMPETING_CONSUMER:
                return "orange";
            case SHARED_DATA:
                return "red";
            default:
                return "black";
        }
    }
}
