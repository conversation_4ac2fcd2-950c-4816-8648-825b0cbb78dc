package com.webank.maling.documentation.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 流程说明书模型
 */
@Data
@Builder
public class FlowDocumentation {
    
    /**
     * 流程名称
     */
    private String flowName;
    
    /**
     * 入口点
     */
    private String entryPoint;
    
    /**
     * 业务描述
     */
    private String businessDescription;
    
    /**
     * API规范
     */
    private ApiSpecification apiSpecification;
    
    /**
     * 数据模型
     */
    private List<DataModel> dataModels;
    
    /**
     * 数据库操作
     */
    private List<DatabaseOperation> databaseOperations;
    
    /**
     * 业务流程
     */
    private BusinessFlow businessFlow;
    
    /**
     * 依赖关系
     */
    private Dependencies dependencies;
    
    /**
     * 异常处理
     */
    private ExceptionHandling exceptionHandling;
    
    /**
     * 代码风格
     */
    private CodeStyle codeStyle;
    
    /**
     * 配置信息
     */
    private List<Configuration> configurations;
    
    /**
     * 流程交互关系
     */
    private List<FlowInteraction> interactions;
    
    /**
     * AI生成的原始文档
     */
    private String rawAiGenerated;
    
    /**
     * 生成时间
     */
    private LocalDateTime generatedTime;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * API规范
     */
    @Data
    @Builder
    public static class ApiSpecification {
        private String method;
        private String path;
        private List<Parameter> requestParameters;
        private ResponseFormat responseFormat;
        private List<Integer> errorCodes;
    }
    
    /**
     * 参数
     */
    @Data
    @Builder
    public static class Parameter {
        private String name;
        private String type;
        private boolean required;
        private String description;
        private String validation;
    }
    
    /**
     * 响应格式
     */
    @Data
    @Builder
    public static class ResponseFormat {
        private int successCode;
        private String dataStructure;
        private String example;
    }
    
    /**
     * 数据模型
     */
    @Data
    @Builder
    public static class DataModel {
        private String name;
        private String tableName;
        private List<Field> fields;
        private String description;
    }
    
    /**
     * 字段
     */
    @Data
    @Builder
    public static class Field {
        private String name;
        private String type;
        private String databaseType;
        private List<String> constraints;
        private String description;
    }
    
    /**
     * 业务流程
     */
    @Data
    @Builder
    public static class BusinessFlow {
        private List<Step> steps;
        private String flowDiagram;
    }
    
    /**
     * 流程步骤
     */
    @Data
    @Builder
    public static class Step {
        private int stepNumber;
        private String description;
        private String codeLocation;
        private List<String> validationRules;
        private String algorithm;
    }
    
    /**
     * 依赖关系
     */
    @Data
    @Builder
    public static class Dependencies {
        private List<InternalService> internalServices;
        private List<ExternalService> externalServices;
    }
    
    /**
     * 内部服务
     */
    @Data
    @Builder
    public static class InternalService {
        private String serviceName;
        private String method;
        private String purpose;
    }
    
    /**
     * 外部服务
     */
    @Data
    @Builder
    public static class ExternalService {
        private String serviceName;
        private String purpose;
        private String endpoint;
    }
    
    /**
     * 异常处理
     */
    @Data
    @Builder
    public static class ExceptionHandling {
        private List<ExceptionCase> exceptions;
    }
    
    /**
     * 异常情况
     */
    @Data
    @Builder
    public static class ExceptionCase {
        private String exceptionType;
        private String trigger;
        private int responseCode;
        private String message;
        private String handlingStrategy;
    }
    
    /**
     * 代码风格
     */
    @Data
    @Builder
    public static class CodeStyle {
        private String namingConvention;
        private String annotationStyle;
        private String architecturePattern;
        private List<String> designPatterns;
    }
    
    /**
     * 配置
     */
    @Data
    @Builder
    public static class Configuration {
        private String key;
        private String value;
        private String description;
        private String type;
    }
}
