package com.webank.maling.documentation.model;

/**
 * SQL操作相关定义
 */
public class SqlOperation {
    
    /**
     * 操作类型枚举
     */
    public enum OperationType {
        SELECT("查询"),
        INSERT("插入"),
        UPDATE("更新"),
        DELETE("删除");
        
        private final String description;
        
        OperationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
