package com.webank.maling.documentation.mapper;

import com.webank.maling.documentation.entity.FlowDocumentationEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 流程说明书Mapper接口
 */
@Mapper
public interface FlowDocumentationMapper {
    
    /**
     * 插入流程说明书
     */
    @Insert("""
        INSERT INTO flow_documentation (
            flow_name, entry_point, entry_point_node_id, documentation, 
            status, progress, current_layer, total_layers, 
            total_nodes, processed_nodes, version, project_id, 
            branch_name, error_message, created_at, updated_at
        ) VALUES (
            #{flowName}, #{entryPoint}, #{entryPointNodeId}, #{documentation},
            #{status}, #{progress}, #{currentLayer}, #{totalLayers},
            #{totalNodes}, #{processedNodes}, #{version}, #{projectId},
            #{branchName}, #{errorMessage}, #{createdAt}, #{updatedAt}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(FlowDocumentationEntity entity);
    
    /**
     * 根据ID查询流程说明书
     */
    @Select("""
        SELECT id, flow_name, entry_point, entry_point_node_id, documentation,
               status, progress, current_layer, total_layers,
               total_nodes, processed_nodes, version, project_id,
               branch_name, error_message, created_at, updated_at, completed_at
        FROM flow_documentation 
        WHERE id = #{id}
    """)
    FlowDocumentationEntity findById(Long id);
    
    /**
     * 根据入口点查询流程说明书
     */
    @Select("""
        SELECT id, flow_name, entry_point, entry_point_node_id, documentation,
               status, progress, current_layer, total_layers,
               total_nodes, processed_nodes, version, project_id,
               branch_name, error_message, created_at, updated_at, completed_at
        FROM flow_documentation 
        WHERE entry_point = #{entryPoint} AND project_id = #{projectId} AND branch_name = #{branchName}
        ORDER BY created_at DESC
    """)
    List<FlowDocumentationEntity> findByEntryPoint(@Param("entryPoint") String entryPoint, 
                                                   @Param("projectId") String projectId, 
                                                   @Param("branchName") String branchName);
    
    /**
     * 查询所有流程说明书
     */
    @Select("""
        SELECT id, flow_name, entry_point, entry_point_node_id, documentation,
               status, progress, current_layer, total_layers,
               total_nodes, processed_nodes, version, project_id,
               branch_name, error_message, created_at, updated_at, completed_at
        FROM flow_documentation 
        WHERE project_id = #{projectId} AND branch_name = #{branchName}
        ORDER BY created_at DESC
    """)
    List<FlowDocumentationEntity> findAll(@Param("projectId") String projectId, 
                                         @Param("branchName") String branchName);
    
    /**
     * 更新流程说明书
     */
    @Update("""
        UPDATE flow_documentation SET
            flow_name = #{flowName},
            documentation = #{documentation},
            status = #{status},
            progress = #{progress},
            current_layer = #{currentLayer},
            total_layers = #{totalLayers},
            total_nodes = #{totalNodes},
            processed_nodes = #{processedNodes},
            error_message = #{errorMessage},
            updated_at = #{updatedAt},
            completed_at = #{completedAt}
        WHERE id = #{id}
    """)
    int update(FlowDocumentationEntity entity);
    
    /**
     * 更新生成进度
     */
    @Update("""
        UPDATE flow_documentation SET
            status = #{status},
            progress = #{progress},
            current_layer = #{currentLayer},
            processed_nodes = #{processedNodes},
            error_message = #{errorMessage},
            updated_at = #{updatedAt}
        WHERE id = #{id}
    """)
    int updateProgress(@Param("id") Long id,
                      @Param("status") String status,
                      @Param("progress") Integer progress,
                      @Param("currentLayer") Integer currentLayer,
                      @Param("processedNodes") Integer processedNodes,
                      @Param("errorMessage") String errorMessage,
                      @Param("updatedAt") java.time.LocalDateTime updatedAt);
    
    /**
     * 根据状态查询流程说明书
     */
    @Select("""
        SELECT id, flow_name, entry_point, entry_point_node_id, documentation,
               status, progress, current_layer, total_layers,
               total_nodes, processed_nodes, version, project_id,
               branch_name, error_message, created_at, updated_at, completed_at
        FROM flow_documentation 
        WHERE status = #{status} AND project_id = #{projectId}
        ORDER BY created_at ASC
    """)
    List<FlowDocumentationEntity> findByStatus(@Param("status") String status, 
                                              @Param("projectId") String projectId);
    
    /**
     * 删除流程说明书
     */
    @Delete("DELETE FROM flow_documentation WHERE id = #{id}")
    int deleteById(Long id);
}
