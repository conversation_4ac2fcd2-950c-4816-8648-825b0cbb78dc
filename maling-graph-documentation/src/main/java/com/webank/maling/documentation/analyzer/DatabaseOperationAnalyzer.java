package com.webank.maling.documentation.analyzer;

import com.webank.maling.documentation.model.DatabaseOperation;
import com.webank.maling.documentation.model.SqlOperation;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据库操作分析器
 * 分析MyBatis Mapper、JPA Repository等数据访问层操作
 */
@Slf4j
public class DatabaseOperationAnalyzer {
    
    private static final Pattern TABLE_PATTERN = Pattern.compile("(?i)(?:FROM|INTO|UPDATE|JOIN)\\s+([a-zA-Z_][a-zA-Z0-9_]*)", Pattern.CASE_INSENSITIVE);
    private static final Pattern FIELD_PATTERN = Pattern.compile("(?i)(?:SELECT|SET|INSERT\\s+INTO\\s+\\w+\\s*\\()\\s*([^)]+)", Pattern.CASE_INSENSITIVE);
    
    /**
     * 分析MyBatis XML Mapper文件
     * 
     * @param mapperXmlPath XML文件路径
     * @return 数据库操作列表
     */
    public List<DatabaseOperation> analyzeMyBatisXmlMapper(String mapperXmlPath) {
        List<DatabaseOperation> operations = new ArrayList<>();
        
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new File(mapperXmlPath));
            
            // 获取namespace
            Element root = document.getDocumentElement();
            String namespace = root.getAttribute("namespace");
            
            // 分析各种SQL操作
            operations.addAll(analyzeSqlElements(document, "select", namespace, SqlOperation.OperationType.SELECT));
            operations.addAll(analyzeSqlElements(document, "insert", namespace, SqlOperation.OperationType.INSERT));
            operations.addAll(analyzeSqlElements(document, "update", namespace, SqlOperation.OperationType.UPDATE));
            operations.addAll(analyzeSqlElements(document, "delete", namespace, SqlOperation.OperationType.DELETE));
            
        } catch (Exception e) {
            log.error("Failed to analyze MyBatis XML mapper: {}", mapperXmlPath, e);
        }
        
        return operations;
    }
    
    /**
     * 分析SQL元素
     */
    private List<DatabaseOperation> analyzeSqlElements(Document document, String elementName, 
                                                     String namespace, SqlOperation.OperationType operationType) {
        List<DatabaseOperation> operations = new ArrayList<>();
        NodeList elements = document.getElementsByTagName(elementName);
        
        for (int i = 0; i < elements.getLength(); i++) {
            Element element = (Element) elements.item(i);
            String id = element.getAttribute("id");
            String sql = element.getTextContent().trim();
            
            DatabaseOperation operation = analyzeSqlStatement(sql, namespace + "." + id, operationType);
            if (operation != null) {
                operations.add(operation);
            }
        }
        
        return operations;
    }
    
    /**
     * 分析SQL语句
     */
    private DatabaseOperation analyzeSqlStatement(String sql, String methodName, SqlOperation.OperationType operationType) {
        try {
            // 清理SQL语句
            String cleanSql = cleanSql(sql);
            
            // 提取表名
            Set<String> tables = extractTables(cleanSql);
            
            // 提取字段
            Set<String> fields = extractFields(cleanSql, operationType);
            
            // 提取参数
            Set<String> parameters = extractParameters(sql);
            
            return DatabaseOperation.builder()
                    .methodName(methodName)
                    .operationType(operationType)
                    .tables(new ArrayList<>(tables))
                    .fields(new ArrayList<>(fields))
                    .parameters(new ArrayList<>(parameters))
                    .sqlTemplate(cleanSql)
                    .build();
                    
        } catch (Exception e) {
            log.warn("Failed to analyze SQL statement: {}", sql, e);
            return null;
        }
    }
    
    /**
     * 清理SQL语句，移除注释和多余空格
     */
    private String cleanSql(String sql) {
        return sql.replaceAll("--.*", "")
                 .replaceAll("/\\*.*?\\*/", "")
                 .replaceAll("\\s+", " ")
                 .trim();
    }
    
    /**
     * 提取表名
     */
    private Set<String> extractTables(String sql) {
        Set<String> tables = new HashSet<>();
        Matcher matcher = TABLE_PATTERN.matcher(sql);
        
        while (matcher.find()) {
            String table = matcher.group(1).trim();
            if (!table.isEmpty() && !isKeyword(table)) {
                tables.add(table);
            }
        }
        
        return tables;
    }
    
    /**
     * 提取字段名
     */
    private Set<String> extractFields(String sql, SqlOperation.OperationType operationType) {
        Set<String> fields = new HashSet<>();
        
        switch (operationType) {
            case SELECT:
                fields.addAll(extractSelectFields(sql));
                break;
            case INSERT:
                fields.addAll(extractInsertFields(sql));
                break;
            case UPDATE:
                fields.addAll(extractUpdateFields(sql));
                break;
            case DELETE:
                // DELETE通常不需要特定字段，但可能有WHERE条件字段
                fields.addAll(extractWhereFields(sql));
                break;
        }
        
        return fields;
    }
    
    /**
     * 提取SELECT字段
     */
    private Set<String> extractSelectFields(String sql) {
        Set<String> fields = new HashSet<>();
        Pattern pattern = Pattern.compile("(?i)SELECT\\s+(.+?)\\s+FROM", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);
        
        if (matcher.find()) {
            String fieldsPart = matcher.group(1);
            if (!"*".equals(fieldsPart.trim())) {
                String[] fieldArray = fieldsPart.split(",");
                for (String field : fieldArray) {
                    String cleanField = field.trim().replaceAll("\\s+AS\\s+\\w+", "");
                    if (cleanField.contains(".")) {
                        cleanField = cleanField.substring(cleanField.lastIndexOf(".") + 1);
                    }
                    fields.add(cleanField);
                }
            }
        }
        
        return fields;
    }
    
    /**
     * 提取INSERT字段
     */
    private Set<String> extractInsertFields(String sql) {
        Set<String> fields = new HashSet<>();
        Pattern pattern = Pattern.compile("(?i)INSERT\\s+INTO\\s+\\w+\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);
        
        if (matcher.find()) {
            String fieldsPart = matcher.group(1);
            String[] fieldArray = fieldsPart.split(",");
            for (String field : fieldArray) {
                fields.add(field.trim());
            }
        }
        
        return fields;
    }
    
    /**
     * 提取UPDATE字段
     */
    private Set<String> extractUpdateFields(String sql) {
        Set<String> fields = new HashSet<>();
        Pattern pattern = Pattern.compile("(?i)SET\\s+(.+?)(?:\\s+WHERE|$)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);
        
        if (matcher.find()) {
            String setPart = matcher.group(1);
            String[] assignments = setPart.split(",");
            for (String assignment : assignments) {
                String field = assignment.split("=")[0].trim();
                fields.add(field);
            }
        }
        
        return fields;
    }
    
    /**
     * 提取WHERE条件字段
     */
    private Set<String> extractWhereFields(String sql) {
        Set<String> fields = new HashSet<>();
        Pattern pattern = Pattern.compile("(?i)WHERE\\s+(.+?)(?:\\s+ORDER\\s+BY|\\s+GROUP\\s+BY|\\s+LIMIT|$)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);
        
        if (matcher.find()) {
            String wherePart = matcher.group(1);
            // 简单提取字段名（可以进一步优化）
            Pattern fieldPattern = Pattern.compile("\\b([a-zA-Z_][a-zA-Z0-9_]*)\\s*[=<>!]");
            Matcher fieldMatcher = fieldPattern.matcher(wherePart);
            while (fieldMatcher.find()) {
                fields.add(fieldMatcher.group(1));
            }
        }
        
        return fields;
    }
    
    /**
     * 提取参数
     */
    private Set<String> extractParameters(String sql) {
        Set<String> parameters = new HashSet<>();
        
        // MyBatis参数格式：#{param} 或 ${param}
        Pattern pattern = Pattern.compile("[#$]\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(sql);
        
        while (matcher.find()) {
            parameters.add(matcher.group(1));
        }
        
        return parameters;
    }
    
    /**
     * 判断是否为SQL关键字
     */
    private boolean isKeyword(String word) {
        Set<String> keywords = Set.of("SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", 
                                     "INTO", "SET", "VALUES", "AND", "OR", "NOT", "NULL", 
                                     "ORDER", "BY", "GROUP", "HAVING", "LIMIT");
        return keywords.contains(word.toUpperCase());
    }
}
