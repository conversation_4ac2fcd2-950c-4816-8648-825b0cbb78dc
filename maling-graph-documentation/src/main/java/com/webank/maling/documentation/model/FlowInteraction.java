package com.webank.maling.documentation.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 流程交互模型
 */
@Data
@Builder
public class FlowInteraction {
    
    /**
     * 生产者流程
     */
    private String producerFlow;
    
    /**
     * 消费者流程
     */
    private String consumerFlow;
    
    /**
     * 交互类型
     */
    private InteractionType interactionType;
    
    /**
     * 数据表
     */
    private String dataTable;
    
    /**
     * 共享字段列表
     */
    private List<String> sharedFields;
    
    /**
     * 交互描述
     */
    private String description;
    
    /**
     * 交互强度（可选，用于表示交互的频繁程度）
     */
    private Integer interactionStrength;
    
    /**
     * 交互类型枚举
     */
    public enum InteractionType {
        PRODUCER_CONSUMER("生产者-消费者"),
        COMPETING_CONSUMER("竞争消费者"),
        SHARED_DATA("共享数据"),
        EVENT_DRIVEN("事件驱动"),
        STATE_MACHINE("状态机");
        
        private final String description;
        
        InteractionType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
