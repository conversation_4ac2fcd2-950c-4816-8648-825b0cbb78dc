package com.webank.maling.documentation.ai;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatClient;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Spring AI文档生成器
 * 使用Spring AI框架调用OpenAI生成流程说明书
 */
@Slf4j
@Component
public class SpringAIDocumentGenerator {
    
    private final ChatClient chatClient;
    
    @Autowired
    public SpringAIDocumentGenerator(ChatClient chatClient) {
        this.chatClient = chatClient;
    }
    
    /**
     * 生成流程说明书
     * 
     * @param prompt 提示词
     * @return 生成的说明书内容
     */
    public String generateDocumentation(String prompt) {
        try {
            log.info("开始生成流程说明书，提示词长度: {}", prompt.length());
            
            // 创建用户消息
            Message userMessage = new UserMessage(prompt);
            
            // 创建提示
            Prompt chatPrompt = new Prompt(List.of(userMessage), 
                OpenAiChatOptions.builder()
                    .withModel("gpt-4")
                    .withTemperature(0.3f)
                    .withMaxTokens(4000)
                    .build());
            
            // 调用AI生成内容
            ChatResponse response = chatClient.call(chatPrompt);
            
            String content = response.getResult().getOutput().getContent();
            log.info("成功生成流程说明书，内容长度: {}", content.length());
            
            return content;
            
        } catch (Exception e) {
            log.error("生成流程说明书失败", e);
            throw new RuntimeException("生成流程说明书失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成分块说明书
     * 用于处理大型子图，分块生成避免token限制
     * 
     * @param prompts 分块的提示词列表
     * @return 合并后的说明书内容
     */
    public String generateChunkedDocumentation(List<String> prompts) {
        if (prompts == null || prompts.isEmpty()) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < prompts.size(); i++) {
            try {
                log.info("生成第 {}/{} 块说明书", i + 1, prompts.size());
                
                String chunkContent = generateDocumentation(prompts.get(i));
                result.append(chunkContent);
                
                if (i < prompts.size() - 1) {
                    result.append("\n\n---\n\n");
                }
                
                // 添加延迟避免API限制
                Thread.sleep(1000);
                
            } catch (Exception e) {
                log.error("生成第 {} 块说明书失败", i + 1, e);
                result.append(String.format("\n\n[第%d块生成失败: %s]\n\n", i + 1, e.getMessage()));
            }
        }
        
        return result.toString();
    }
    
    /**
     * 生成概要说明书
     * 用于大型流程的概要生成
     * 
     * @param prompt 提示词
     * @return 概要说明书
     */
    public String generateSummaryDocumentation(String prompt) {
        try {
            log.info("开始生成概要说明书");
            
            String summaryPrompt = "请基于以下信息生成简洁的概要说明书，重点关注：\n" +
                    "1. 核心业务流程\n" +
                    "2. 主要组件和依赖\n" +
                    "3. 关键数据流转\n" +
                    "4. 重要的设计决策\n\n" + prompt;
            
            Message userMessage = new UserMessage(summaryPrompt);
            
            Prompt chatPrompt = new Prompt(List.of(userMessage), 
                OpenAiChatOptions.builder()
                    .withModel("gpt-4")
                    .withTemperature(0.2f)
                    .withMaxTokens(2000)
                    .build());
            
            ChatResponse response = chatClient.call(chatPrompt);
            String content = response.getResult().getOutput().getContent();
            
            log.info("成功生成概要说明书，内容长度: {}", content.length());
            return content;
            
        } catch (Exception e) {
            log.error("生成概要说明书失败", e);
            throw new RuntimeException("生成概要说明书失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成增量说明书
     * 用于更新已有说明书的特定部分
     * 
     * @param existingDoc 已有说明书
     * @param newContent 新增内容
     * @param updateType 更新类型
     * @return 更新后的说明书
     */
    public String generateIncrementalDocumentation(String existingDoc, String newContent, String updateType) {
        try {
            log.info("开始生成增量说明书，更新类型: {}", updateType);
            
            String incrementalPrompt = String.format(
                "请基于现有说明书和新增内容，生成更新后的完整说明书。\n\n" +
                "更新类型: %s\n\n" +
                "现有说明书:\n%s\n\n" +
                "新增内容:\n%s\n\n" +
                "请保持原有结构，合理整合新增内容，确保文档的连贯性和完整性。",
                updateType, existingDoc, newContent
            );
            
            return generateDocumentation(incrementalPrompt);
            
        } catch (Exception e) {
            log.error("生成增量说明书失败", e);
            throw new RuntimeException("生成增量说明书失败: " + e.getMessage(), e);
        }
    }
}
