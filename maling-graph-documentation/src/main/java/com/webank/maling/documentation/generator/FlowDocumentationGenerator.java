package com.webank.maling.documentation.generator;

import com.webank.maling.ai.vector.OpenAIVectorGenerator;
import com.webank.maling.base.model.FunctionNode;
import com.webank.maling.documentation.analyzer.DatabaseOperationAnalyzer;
import com.webank.maling.documentation.analyzer.FlowInteractionAnalyzer;
import com.webank.maling.documentation.model.DatabaseOperation;
import com.webank.maling.documentation.model.FlowDocumentation;
import com.webank.maling.documentation.model.FlowInteraction;
import com.webank.maling.repository.nebula.NebulaGraphClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程说明书生成器
 * 基于代码图谱生成详细的流程说明书
 */
@Slf4j
public class FlowDocumentationGenerator {
    
    private final NebulaGraphClient nebulaClient;
    private final OpenAIVectorGenerator aiGenerator;
    private final DatabaseOperationAnalyzer dbAnalyzer;
    private final FlowInteractionAnalyzer interactionAnalyzer;
    
    public FlowDocumentationGenerator(NebulaGraphClient nebulaClient, 
                                    OpenAIVectorGenerator aiGenerator) {
        this.nebulaClient = nebulaClient;
        this.aiGenerator = aiGenerator;
        this.dbAnalyzer = new DatabaseOperationAnalyzer();
        this.interactionAnalyzer = new FlowInteractionAnalyzer();
    }
    
    /**
     * 生成所有入口流程的说明书
     * 
     * @return 流程说明书列表
     */
    public List<FlowDocumentation> generateAllFlowDocumentations() {
        List<FlowDocumentation> documentations = new ArrayList<>();
        
        try {
            // 1. 获取所有入口点
            List<FunctionNode> entryPoints = getEntryPoints();
            log.info("Found {} entry points", entryPoints.size());
            
            // 2. 为每个入口点生成流程说明书
            for (FunctionNode entryPoint : entryPoints) {
                FlowDocumentation documentation = generateFlowDocumentation(entryPoint);
                if (documentation != null) {
                    documentations.add(documentation);
                }
            }
            
            // 3. 分析流程间交互
            analyzeAndAddFlowInteractions(documentations);
            
            // 4. 生成系统级说明书
            generateSystemDocumentation(documentations);
            
        } catch (Exception e) {
            log.error("Failed to generate flow documentations", e);
        }
        
        return documentations;
    }
    
    /**
     * 生成单个流程的说明书
     */
    public FlowDocumentation generateFlowDocumentation(FunctionNode entryPoint) {
        try {
            log.info("Generating documentation for entry point: {}", entryPoint.getFullName());
            
            // 1. 收集流程相关的代码
            FlowCodeContext codeContext = collectFlowCode(entryPoint);
            
            // 2. 分析数据库操作
            List<DatabaseOperation> dbOperations = analyzeFlowDatabaseOperations(codeContext);
            
            // 3. 构建说明书上下文
            String documentationPrompt = buildDocumentationPrompt(entryPoint, codeContext, dbOperations);
            
            // 4. 调用AI生成说明书
            String aiGeneratedDoc = generateAIDocumentation(documentationPrompt);
            
            // 5. 构建结构化说明书
            FlowDocumentation documentation = FlowDocumentation.builder()
                    .flowName(extractFlowName(entryPoint))
                    .entryPoint(entryPoint.getFullName())
                    .businessDescription(extractBusinessDescription(aiGeneratedDoc))
                    .apiSpecification(extractApiSpecification(entryPoint, aiGeneratedDoc))
                    .dataModels(extractDataModels(codeContext, aiGeneratedDoc))
                    .databaseOperations(dbOperations)
                    .businessFlow(extractBusinessFlow(aiGeneratedDoc))
                    .dependencies(extractDependencies(codeContext, aiGeneratedDoc))
                    .exceptionHandling(extractExceptionHandling(codeContext, aiGeneratedDoc))
                    .codeStyle(extractCodeStyle(codeContext, aiGeneratedDoc))
                    .configurations(extractConfigurations(codeContext, aiGeneratedDoc))
                    .rawAiGenerated(aiGeneratedDoc)
                    .build();
            
            return documentation;
            
        } catch (Exception e) {
            log.error("Failed to generate documentation for entry point: {}", entryPoint.getFullName(), e);
            return null;
        }
    }
    
    /**
     * 获取所有入口点
     */
    private List<FunctionNode> getEntryPoints() {
        // 从NebulaGraph查询所有is_entry_point=true的函数节点
        String query = "MATCH (f:function) WHERE f.is_entry_point == true RETURN f";
        // 这里需要实现具体的查询逻辑
        return new ArrayList<>(); // 临时返回空列表
    }
    
    /**
     * 收集流程相关的代码
     */
    private FlowCodeContext collectFlowCode(FunctionNode entryPoint) {
        // 1. 从入口点开始，通过图谱遍历收集相关代码
        // 2. 包括调用链、数据模型、配置等
        // 3. 构建完整的代码上下文
        
        return FlowCodeContext.builder()
                .entryPointCode(getMethodCode(entryPoint))
                .calledMethods(getCalledMethods(entryPoint))
                .dataModels(getRelatedDataModels(entryPoint))
                .configurations(getRelatedConfigurations(entryPoint))
                .build();
    }
    
    /**
     * 分析流程的数据库操作
     */
    private List<DatabaseOperation> analyzeFlowDatabaseOperations(FlowCodeContext codeContext) {
        List<DatabaseOperation> operations = new ArrayList<>();
        
        // 1. 分析MyBatis XML文件
        for (String mapperXml : codeContext.getMapperXmlFiles()) {
            operations.addAll(dbAnalyzer.analyzeMyBatisXmlMapper(mapperXml));
        }
        
        // 2. 分析注解形式的Mapper
        // 3. 分析JPA Repository
        // 4. 分析直接的JDBC操作
        
        return operations;
    }
    
    /**
     * 构建AI生成说明书的提示词
     */
    private String buildDocumentationPrompt(FunctionNode entryPoint, 
                                          FlowCodeContext codeContext, 
                                          List<DatabaseOperation> dbOperations) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请基于以下Java代码生成详细的流程说明书，包括系统设计、数据字段、设计风格等信息：\n\n");
        
        // 入口点信息
        prompt.append("## 入口点信息\n");
        prompt.append("方法名: ").append(entryPoint.getFullName()).append("\n");
        prompt.append("可见性: ").append(entryPoint.getVisibility()).append("\n");
        prompt.append("是否静态: ").append(entryPoint.getIsStatic()).append("\n\n");
        
        // 代码内容
        prompt.append("## 主要代码\n");
        prompt.append("```java\n");
        prompt.append(codeContext.getEntryPointCode()).append("\n");
        prompt.append("```\n\n");
        
        // 调用的方法
        if (!codeContext.getCalledMethods().isEmpty()) {
            prompt.append("## 调用的方法\n");
            for (String method : codeContext.getCalledMethods()) {
                prompt.append("- ").append(method).append("\n");
            }
            prompt.append("\n");
        }
        
        // 数据库操作
        if (!dbOperations.isEmpty()) {
            prompt.append("## 数据库操作\n");
            for (DatabaseOperation op : dbOperations) {
                prompt.append("- ").append(op.getOperationType().getDescription())
                      .append(" 表: ").append(String.join(", ", op.getTables()))
                      .append(" 字段: ").append(String.join(", ", op.getFields()))
                      .append("\n");
            }
            prompt.append("\n");
        }
        
        // 数据模型
        if (!codeContext.getDataModels().isEmpty()) {
            prompt.append("## 相关数据模型\n");
            for (String model : codeContext.getDataModels()) {
                prompt.append("```java\n").append(model).append("\n```\n\n");
            }
        }
        
        prompt.append("请生成包含以下内容的说明书：\n");
        prompt.append("1. 业务描述：该流程的主要业务功能\n");
        prompt.append("2. API规范：请求参数、响应格式、状态码\n");
        prompt.append("3. 数据模型：涉及的实体类和字段说明\n");
        prompt.append("4. 业务流程：详细的处理步骤\n");
        prompt.append("5. 依赖关系：内部服务和外部服务依赖\n");
        prompt.append("6. 异常处理：可能的异常情况和处理方式\n");
        prompt.append("7. 代码风格：命名规范、注解使用、架构模式\n");
        prompt.append("8. 配置信息：相关的配置项\n");
        
        return prompt.toString();
    }
    
    /**
     * 调用AI生成说明书
     */
    private String generateAIDocumentation(String prompt) {
        try {
            // 这里需要调用OpenAI API生成文档
            // 由于当前只有向量生成功能，这里返回模拟结果
            return "AI生成的说明书内容...";
        } catch (Exception e) {
            log.error("Failed to generate AI documentation", e);
            return "生成说明书失败";
        }
    }
    
    // 以下是提取方法的占位符实现
    private String extractFlowName(FunctionNode entryPoint) {
        return entryPoint.getName();
    }
    
    private String extractBusinessDescription(String aiDoc) {
        return "从AI生成的文档中提取业务描述";
    }
    
    private Object extractApiSpecification(FunctionNode entryPoint, String aiDoc) {
        return new Object(); // 临时实现
    }
    
    private Object extractDataModels(FlowCodeContext codeContext, String aiDoc) {
        return new Object(); // 临时实现
    }
    
    private Object extractBusinessFlow(String aiDoc) {
        return new Object(); // 临时实现
    }
    
    private Object extractDependencies(FlowCodeContext codeContext, String aiDoc) {
        return new Object(); // 临时实现
    }
    
    private Object extractExceptionHandling(FlowCodeContext codeContext, String aiDoc) {
        return new Object(); // 临时实现
    }
    
    private Object extractCodeStyle(FlowCodeContext codeContext, String aiDoc) {
        return new Object(); // 临时实现
    }
    
    private Object extractConfigurations(FlowCodeContext codeContext, String aiDoc) {
        return new Object(); // 临时实现
    }
    
    private String getMethodCode(FunctionNode entryPoint) {
        return "方法代码内容"; // 临时实现
    }
    
    private List<String> getCalledMethods(FunctionNode entryPoint) {
        return new ArrayList<>(); // 临时实现
    }
    
    private List<String> getRelatedDataModels(FunctionNode entryPoint) {
        return new ArrayList<>(); // 临时实现
    }
    
    private List<String> getRelatedConfigurations(FunctionNode entryPoint) {
        return new ArrayList<>(); // 临时实现
    }
    
    private void analyzeAndAddFlowInteractions(List<FlowDocumentation> documentations) {
        // 分析流程间交互并添加到说明书中
    }
    
    private void generateSystemDocumentation(List<FlowDocumentation> documentations) {
        // 生成系统级说明书
    }
}
