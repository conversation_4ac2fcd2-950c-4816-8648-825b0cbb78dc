package com.webank.maling.documentation.mapper;

import com.webank.maling.documentation.entity.MethodInfoEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 方法信息Mapper接口
 */
@Mapper
public interface MethodInfoMapper {
    
    /**
     * 插入方法信息
     */
    @Insert("""
        INSERT INTO method_info (
            documentation_id, node_id, method_full_name, method_name,
            class_name, package_name, method_signature, return_type,
            parameters, visibility, is_static, is_abstract,
            call_level, method_type, description, code_snippet,
            file_path, line_number, created_at, updated_at
        ) VALUES (
            #{documentationId}, #{nodeId}, #{methodFullName}, #{methodName},
            #{className}, #{packageName}, #{methodSignature}, #{returnType},
            #{parameters}, #{visibility}, #{isStatic}, #{isAbstract},
            #{callLevel}, #{methodType}, #{description}, #{codeSnippet},
            #{filePath}, #{lineNumber}, #{createdAt}, #{updatedAt}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(MethodInfoEntity entity);
    
    /**
     * 批量插入方法信息
     */
    @Insert({
        "<script>",
        "INSERT INTO method_info (",
        "documentation_id, node_id, method_full_name, method_name,",
        "class_name, package_name, method_signature, return_type,",
        "parameters, visibility, is_static, is_abstract,",
        "call_level, method_type, description, code_snippet,",
        "file_path, line_number, created_at, updated_at",
        ") VALUES ",
        "<foreach collection='list' item='item' separator=','>",
        "(#{item.documentationId}, #{item.nodeId}, #{item.methodFullName}, #{item.methodName},",
        "#{item.className}, #{item.packageName}, #{item.methodSignature}, #{item.returnType},",
        "#{item.parameters}, #{item.visibility}, #{item.isStatic}, #{item.isAbstract},",
        "#{item.callLevel}, #{item.methodType}, #{item.description}, #{item.codeSnippet},",
        "#{item.filePath}, #{item.lineNumber}, #{item.createdAt}, #{item.updatedAt})",
        "</foreach>",
        "</script>"
    })
    int batchInsert(List<MethodInfoEntity> entities);
    
    /**
     * 根据ID查询方法信息
     */
    @Select("""
        SELECT id, documentation_id, node_id, method_full_name, method_name,
               class_name, package_name, method_signature, return_type,
               parameters, visibility, is_static, is_abstract,
               call_level, method_type, description, code_snippet,
               file_path, line_number, created_at, updated_at
        FROM method_info 
        WHERE id = #{id}
    """)
    MethodInfoEntity findById(Long id);
    
    /**
     * 根据说明书ID查询所有方法信息
     */
    @Select("""
        SELECT id, documentation_id, node_id, method_full_name, method_name,
               class_name, package_name, method_signature, return_type,
               parameters, visibility, is_static, is_abstract,
               call_level, method_type, description, code_snippet,
               file_path, line_number, created_at, updated_at
        FROM method_info 
        WHERE documentation_id = #{documentationId}
        ORDER BY call_level ASC, method_full_name ASC
    """)
    List<MethodInfoEntity> findByDocumentationId(Long documentationId);
    
    /**
     * 根据说明书ID和调用层级查询方法信息
     */
    @Select("""
        SELECT id, documentation_id, node_id, method_full_name, method_name,
               class_name, package_name, method_signature, return_type,
               parameters, visibility, is_static, is_abstract,
               call_level, method_type, description, code_snippet,
               file_path, line_number, created_at, updated_at
        FROM method_info 
        WHERE documentation_id = #{documentationId} AND call_level = #{callLevel}
        ORDER BY method_full_name ASC
    """)
    List<MethodInfoEntity> findByDocumentationIdAndCallLevel(@Param("documentationId") Long documentationId,
                                                            @Param("callLevel") Integer callLevel);
    
    /**
     * 根据节点ID查询方法信息
     */
    @Select("""
        SELECT id, documentation_id, node_id, method_full_name, method_name,
               class_name, package_name, method_signature, return_type,
               parameters, visibility, is_static, is_abstract,
               call_level, method_type, description, code_snippet,
               file_path, line_number, created_at, updated_at
        FROM method_info 
        WHERE node_id = #{nodeId}
    """)
    List<MethodInfoEntity> findByNodeId(String nodeId);
    
    /**
     * 更新方法信息
     */
    @Update("""
        UPDATE method_info SET
            method_full_name = #{methodFullName},
            method_name = #{methodName},
            class_name = #{className},
            package_name = #{packageName},
            method_signature = #{methodSignature},
            return_type = #{returnType},
            parameters = #{parameters},
            visibility = #{visibility},
            is_static = #{isStatic},
            is_abstract = #{isAbstract},
            call_level = #{callLevel},
            method_type = #{methodType},
            description = #{description},
            code_snippet = #{codeSnippet},
            file_path = #{filePath},
            line_number = #{lineNumber},
            updated_at = #{updatedAt}
        WHERE id = #{id}
    """)
    int update(MethodInfoEntity entity);
    
    /**
     * 根据说明书ID删除所有方法信息
     */
    @Delete("DELETE FROM method_info WHERE documentation_id = #{documentationId}")
    int deleteByDocumentationId(Long documentationId);
    
    /**
     * 根据ID删除方法信息
     */
    @Delete("DELETE FROM method_info WHERE id = #{id}")
    int deleteById(Long id);
}
