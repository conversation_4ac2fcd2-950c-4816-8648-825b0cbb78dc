# {{flowName}} 流程说明书

## 基础信息
- **流程名称**: {{flowName}}
- **入口点**: {{entryPoint}}
- **生成时间**: {{generatedTime}}
- **版本**: {{version}}

## 业务描述
{{businessDescription}}

## API规范

### 请求信息
- **HTTP方法**: {{apiSpecification.method}}
- **请求路径**: {{apiSpecification.path}}

### 请求参数
{{#each apiSpecification.requestParameters}}
| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| {{name}} | {{type}} | {{#if required}}是{{else}}否{{/if}} | {{description}} | {{validation}} |
{{/each}}

### 响应格式
- **成功状态码**: {{apiSpecification.responseFormat.successCode}}
- **数据结构**: {{apiSpecification.responseFormat.dataStructure}}
- **响应示例**:
```json
{{apiSpecification.responseFormat.example}}
```

### 错误状态码
{{#each apiSpecification.errorCodes}}
- {{this}}
{{/each}}

## 数据模型

{{#each dataModels}}
### {{name}}
- **表名**: {{tableName}}
- **描述**: {{description}}

| 字段名 | Java类型 | 数据库类型 | 约束 | 描述 |
|--------|----------|------------|------|------|
{{#each fields}}
| {{name}} | {{type}} | {{databaseType}} | {{constraints}} | {{description}} |
{{/each}}

{{/each}}

## 数据库操作

{{#each databaseOperations}}
### {{operationType.description}}操作
- **方法**: {{methodName}}
- **涉及表**: {{tables}}
- **操作字段**: {{fields}}
- **SQL模板**:
```sql
{{sqlTemplate}}
```
- **参数**: {{parameters}}

{{/each}}

## 业务流程

{{#each businessFlow.steps}}
### 步骤 {{stepNumber}}: {{description}}
- **代码位置**: {{codeLocation}}
{{#if validationRules}}
- **验证规则**: 
{{#each validationRules}}
  - {{this}}
{{/each}}
{{/if}}
{{#if algorithm}}
- **算法**: {{algorithm}}
{{/if}}

{{/each}}

{{#if businessFlow.flowDiagram}}
### 流程图
```mermaid
{{businessFlow.flowDiagram}}
```
{{/if}}

## 依赖关系

### 内部服务依赖
{{#each dependencies.internalServices}}
- **{{serviceName}}**: {{method}} - {{purpose}}
{{/each}}

### 外部服务依赖
{{#each dependencies.externalServices}}
- **{{serviceName}}**: {{endpoint}} - {{purpose}}
{{/each}}

## 异常处理

{{#each exceptionHandling.exceptions}}
### {{exceptionType}}
- **触发条件**: {{trigger}}
- **响应状态码**: {{responseCode}}
- **错误信息**: {{message}}
- **处理策略**: {{handlingStrategy}}

{{/each}}

## 代码风格

- **命名规范**: {{codeStyle.namingConvention}}
- **注解风格**: {{codeStyle.annotationStyle}}
- **架构模式**: {{codeStyle.architecturePattern}}
- **设计模式**: 
{{#each codeStyle.designPatterns}}
  - {{this}}
{{/each}}

## 配置信息

| 配置项 | 默认值 | 类型 | 描述 |
|--------|--------|------|------|
{{#each configurations}}
| {{key}} | {{value}} | {{type}} | {{description}} |
{{/each}}

## 流程交互关系

{{#each interactions}}
### {{interactionType.description}}
- **生产者流程**: {{producerFlow}}
- **消费者流程**: {{consumerFlow}}
- **数据表**: {{dataTable}}
- **共享字段**: {{sharedFields}}
- **描述**: {{description}}

{{/each}}

## 技术细节

### 关键代码片段
```java
// 入口点代码
{{entryPointCode}}
```

### 调用链
{{#each calledMethods}}
- {{this}}
{{/each}}

## 注意事项

1. **性能考虑**: 
   - 数据库查询优化建议
   - 缓存策略
   - 并发处理

2. **安全考虑**:
   - 输入验证
   - 权限控制
   - 数据脱敏

3. **监控告警**:
   - 关键指标
   - 异常监控
   - 性能监控

## 扩展建议

1. **功能扩展**:
   - 可能的新功能点
   - 扩展接口设计

2. **性能优化**:
   - 优化建议
   - 架构改进

3. **维护建议**:
   - 代码重构建议
   - 技术债务

---

*本说明书由AI自动生成，基于代码图谱分析结果*
