-- 流程说明书表
CREATE TABLE IF NOT EXISTS flow_documentation (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    flow_name VARCHAR(255) NOT NULL COMMENT '流程名称',
    entry_point VARCHAR(1000) NOT NULL COMMENT '入口点方法全名',
    entry_point_node_id VARCHAR(255) NOT NULL COMMENT '入口点节点ID（在图数据库中的ID）',
    documentation MEDIUMTEXT COMMENT '说明书内容（AI生成的完整说明书）',
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING' COMMENT '生成状态：PENDING, IN_PROGRESS, COMPLETED, FAILED',
    progress INT DEFAULT 0 COMMENT '生成进度（0-100）',
    current_layer INT DEFAULT 0 COMMENT '当前处理的层级',
    total_layers INT DEFAULT 0 COMMENT '总层级数',
    total_nodes INT DEFAULT 0 COMMENT '涉及的节点总数',
    processed_nodes INT DEFAULT 0 COMMENT '已处理的节点数',
    version VARCHAR(50) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    project_id VARCHAR(255) NOT NULL COMMENT '项目ID',
    branch_name VARCHAR(255) NOT NULL COMMENT '分支名称',
    error_message TEXT COMMENT '错误信息（如果生成失败）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at DATETIME COMMENT '完成时间',
    
    INDEX idx_entry_point (entry_point(255)),
    INDEX idx_project_branch (project_id, branch_name),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_entry_point_project_branch (entry_point(255), project_id, branch_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='流程说明书表';

-- 方法信息表
CREATE TABLE IF NOT EXISTS method_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    documentation_id BIGINT NOT NULL COMMENT '说明书ID（外键）',
    node_id VARCHAR(255) NOT NULL COMMENT '方法节点ID（在图数据库中的ID）',
    method_full_name VARCHAR(1000) NOT NULL COMMENT '方法全名',
    method_name VARCHAR(255) NOT NULL COMMENT '方法名',
    class_name VARCHAR(500) NOT NULL COMMENT '类名',
    package_name VARCHAR(500) COMMENT '包名',
    method_signature VARCHAR(2000) COMMENT '方法签名',
    return_type VARCHAR(255) COMMENT '返回类型',
    parameters JSON COMMENT '参数列表（JSON格式）',
    visibility VARCHAR(20) COMMENT '可见性：public, private, protected, package',
    is_static BOOLEAN DEFAULT FALSE COMMENT '是否静态方法',
    is_abstract BOOLEAN DEFAULT FALSE COMMENT '是否抽象方法',
    call_level INT NOT NULL DEFAULT 0 COMMENT '调用层级（从入口点开始的深度）',
    method_type VARCHAR(50) COMMENT '方法类型：ENTRY_POINT, CONTROLLER, SERVICE, REPOSITORY, UTILITY, OTHER',
    description TEXT COMMENT '方法描述（从注释中提取）',
    code_snippet MEDIUMTEXT COMMENT '方法代码片段',
    file_path VARCHAR(1000) COMMENT '文件路径',
    line_number INT COMMENT '行号',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_documentation_id (documentation_id),
    INDEX idx_node_id (node_id),
    INDEX idx_method_full_name (method_full_name(255)),
    INDEX idx_call_level (call_level),
    INDEX idx_method_type (method_type),
    INDEX idx_class_name (class_name(255)),
    
    FOREIGN KEY (documentation_id) REFERENCES flow_documentation(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='方法信息表';
